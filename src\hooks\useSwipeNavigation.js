import { useEffect, useRef } from 'react';

/**
 * Hook để xử lý swipe navigation
 * @param {Object} options - Configuration options
 * @returns {void}
 */
const useSwipeNavigation = (options = {}) => {
    const {
        threshold = 80, // Minimum distance for swipe
        minVelocity = 0.3, // Minimum velocity for swipe
        swipeDirection = "right", // Direction to detect ("left", "right", "up", "down")
        preventDefaultOnSwipe = true, // Prevent default touch behavior
        onSwipe = () => {}, // Callback when swipe is detected
        enabled = true // Enable/disable the hook
    } = options;

    const touchStartRef = useRef(null);
    const touchEndRef = useRef(null);
    const startTimeRef = useRef(null);

    const handleTouchStart = (e) => {
        if (!enabled) return;
        
        touchStartRef.current = {
            x: e.touches[0].clientX,
            y: e.touches[0].clientY
        };
        startTimeRef.current = Date.now();
    };

    const handleTouchMove = (e) => {
        if (!enabled || !touchStartRef.current) return;
        
        if (preventDefaultOnSwipe) {
            // Only prevent default if we detect a potential swipe
            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const deltaX = Math.abs(currentX - touchStartRef.current.x);
            const deltaY = Math.abs(currentY - touchStartRef.current.y);
            
            // If horizontal swipe is dominant, prevent vertical scroll
            if (deltaX > deltaY && deltaX > 20) {
                e.preventDefault();
            }
        }
    };

    const handleTouchEnd = (e) => {
        if (!enabled || !touchStartRef.current || !startTimeRef.current) return;

        touchEndRef.current = {
            x: e.changedTouches[0].clientX,
            y: e.changedTouches[0].clientY
        };

        const deltaX = touchEndRef.current.x - touchStartRef.current.x;
        const deltaY = touchEndRef.current.y - touchStartRef.current.y;
        const deltaTime = Date.now() - startTimeRef.current;
        
        // Calculate velocity (pixels per millisecond)
        const velocity = Math.sqrt(deltaX * deltaX + deltaY * deltaY) / deltaTime;
        
        let isSwipe = false;
        
        // Check if swipe meets threshold and velocity requirements
        if (velocity >= minVelocity) {
            switch (swipeDirection) {
                case "right":
                    isSwipe = deltaX > threshold && Math.abs(deltaY) < threshold;
                    break;
                case "left":
                    isSwipe = deltaX < -threshold && Math.abs(deltaY) < threshold;
                    break;
                case "up":
                    isSwipe = deltaY < -threshold && Math.abs(deltaX) < threshold;
                    break;
                case "down":
                    isSwipe = deltaY > threshold && Math.abs(deltaX) < threshold;
                    break;
                default:
                    break;
            }
        }

        if (isSwipe) {
            onSwipe({
                direction: swipeDirection,
                deltaX,
                deltaY,
                velocity,
                duration: deltaTime
            });
        }

        // Reset
        touchStartRef.current = null;
        touchEndRef.current = null;
        startTimeRef.current = null;
    };

    useEffect(() => {
        if (!enabled) return;

        const element = document.body;

        // Add event listeners
        element.addEventListener('touchstart', handleTouchStart, { passive: true });
        element.addEventListener('touchmove', handleTouchMove, { passive: false });
        element.addEventListener('touchend', handleTouchEnd, { passive: true });

        // Cleanup
        return () => {
            element.removeEventListener('touchstart', handleTouchStart);
            element.removeEventListener('touchmove', handleTouchMove);
            element.removeEventListener('touchend', handleTouchEnd);
        };
    }, [enabled, threshold, minVelocity, swipeDirection, preventDefaultOnSwipe]);

    // Return nothing - this is a side-effect hook
    return null;
};

export default useSwipeNavigation; 