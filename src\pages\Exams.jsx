import { useCallback, useContext, useEffect, useState } from 'react';
import { Box, Text, Button } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { authApi } from '../utils/api'; // Giả sử bạn có file API util
import { useNavigate, useLocation } from 'zmp-ui';
import Loading from '../components/Loading';
import useApiCache from '../hooks/useApiCache';
import { AuthContext } from '../context/AuthContext';
import useSwipeNavigation from '../hooks/useSwipeNavigation';
import { getSubjectIcon } from '../utils/subjectUtils';

const Exercises = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { user } = useContext(AuthContext);

    console.log('=== EXERCISES COMPONENT MOUNTED ===');
    console.log('User from context:', user);
    console.log('AuthContext:', AuthContext);
    console.log('authApi:', authApi);
    console.log('=======================================');

    // Test immediate effect
    useEffect(() => {
        console.log('[useEffect] Component mounted, user:', user);
        console.log('[useEffect] Testing authApi availability:', !!authApi);
        
        // Test direct API call
        const testApiCall = async () => {
            try {
                console.log('[testApiCall] Attempting direct API call...');
                const response = await authApi.get('/subjects');
                console.log('[testApiCall] Direct API success:', response.data);
            } catch (err) {
                console.error('[testApiCall] Direct API error:', err);
            }
        };
        
        testApiCall();
    }, [user]);

    // Tạo hàm gọi API lấy danh sách môn học
    const fetchSubjects = useCallback(async () => {
        console.log('[fetchSubjects] Called!');
        try {
            console.log('[fetchSubjects] Making API call...');
            const response = await authApi.get('/subjects');
            console.log('[fetchSubjects] API response:', response.data);
            if (response.data.success) {
                return response.data.data || [];
            } else {
                throw new Error('Không thể lấy danh sách môn học');
            }
        } catch (err) {
            console.error('[fetchSubjects] Error:', err);
            throw new Error('Lỗi khi lấy danh sách môn học');
        }
    }, []);

    console.log('[Exercises] About to call useApiCache for subjects');

    // BACKUP: Test without useApiCache
    const [testSubjects, setTestSubjects] = useState([]);
    const [testLoading, setTestLoading] = useState(false);
    
    useEffect(() => {
        const fetchTestData = async () => {
            console.log('[testFetch] Starting...');
            setTestLoading(true);
            try {
                const response = await authApi.get('/subjects');
                console.log('[testFetch] Success:', response.data);
                setTestSubjects(response.data.data || []);
            } catch (err) {
                console.error('[testFetch] Error:', err);
            } finally {
                setTestLoading(false);
            }
        };
        
        fetchTestData();
    }, []);

    // Sử dụng hook useApiCache để lấy và cache danh sách môn học
    const {
        data: subjectsData = [],
        loading: subjectsLoading,
        error: subjectsError
    } = useApiCache(
        fetchSubjects,
        [], // Không phụ thuộc vào gì, chỉ fetch 1 lần khi mount
        {
            cacheKey: user?._id ? `subjects_${user._id}` : 'subjects_temp', // Fallback cache key
            enabled: true, // Luôn enable, để useApiCache tự handle
            cacheTime: 30 * 60 * 1000, // Cache 30 phút
        }
    );

    console.log('[Exercises] useApiCache returned:', {
        subjectsData,
        subjectsLoading,
        subjectsError
    });
    
    console.log('[Exercises] Test version:', {
        testSubjects,
        testLoading
    });

    // Đảm bảo subjects luôn là array
    const subjects = Array.isArray(subjectsData) ? subjectsData : [];

    // Tạo hàm gọi API lấy thống kê cho tất cả các môn học
    const fetchAllStatistics = useCallback(async () => {
        console.log('fetchAllStatistics called with subjects:', subjects.length);
        if (!subjects || subjects.length === 0) return {};

        try {
            const stats = {};
            // Đảm bảo mỗi môn học đều có thống kê mặc định
            subjects.forEach(subject => {
                if (subject && subject._id) {
                    stats[subject._id] = {
                        totalExams: 0,
                        completedExams: 0,
                        completionRate: '0.00',
                    };
                }
            });

            const promises = subjects.map(subject => {
                if (!subject || !subject._id) return Promise.resolve();

                return authApi.get(`/exams/statistics?subjectId=${subject._id}`)
                    .then(response => {
                        if (response.data.success) {
                            stats[subject._id] = response.data.data.summary;
                        }
                        return null;
                    })
                    .catch(err => {
                        console.error(`Error fetching statistics for subject ${subject._id}:`, err);
                        // Giữ nguyên giá trị mặc định đã thiết lập
                        return null;
                    });
            });

            // Đợi tất cả các request hoàn thành
            await Promise.all(promises);
            console.log('Statistics fetched:', stats);
            return stats;
        } catch (err) {
            console.error('Error fetching statistics:', err);
            // Trả về đối tượng trống thay vì ném lỗi
            return {};
        }
    }, [subjects]);

    // Sử dụng hook useApiCache để lấy và cache thống kê
    const {
        data: statisticsData = {},
        loading: statsLoading,
        error: statsError
    } = useApiCache(
        fetchAllStatistics,
        [subjects.length], // Chỉ trigger khi số lượng subjects thay đổi
        {
            cacheKey: subjects.length > 0 ? `statistics_${user?._id}_${subjects.length}` : '',
            enabled: subjects.length > 0, // Chỉ enable khi có subjects
            cacheTime: 5 * 60 * 1000, // Cache 5 phút
        }
    );

    // Đảm bảo statistics luôn là object
    const statistics = statisticsData || {};

    // Tính toán trạng thái loading và error tổng hợp
    const loading = subjectsLoading || (subjects.length > 0 && statsLoading);
    const error = subjectsError || statsError;

    // Debug log
    console.log('=== EXAMS DEBUG ===');
    console.log('User:', user?._id);
    console.log('Raw subjectsData:', subjectsData);
    console.log('Processed subjects:', subjects);
    console.log('Subjects length:', subjects.length);
    console.log('Subjects loading:', subjectsLoading);
    console.log('Stats loading:', statsLoading);
    console.log('Combined loading:', loading);
    console.log('Error:', error);
    console.log('Statistics:', statistics);
    console.log('==================');

    // Sử dụng hook useSwipeNavigation để hỗ trợ vuốt để quay lại
    useSwipeNavigation({
        threshold: 80,
        minVelocity: 0.3,
        swipeDirection: "right",
        preventDefaultOnSwipe: true
    });

    // Hàm xử lý khi click vào môn học
    const handleSubjectClick = (subjectId) => {
        navigate(`/subjects/${subjectId}/exercises`, {
            replace: false,
            state: {
                tab: 'exams',
                previousPath: location.pathname,
                previousState: location.state
            },
        });
    };

    return (
        <Box
            className="container"
            style={{
                minHeight: '100vh',
                display: 'flex',
                flexDirection: 'column',
                paddingBottom: '60px',
                backgroundColor: '#f5f5f5',
            }}
        >
            <HeaderEdu
                title="Bài tập"
                showBackButton={true}
                onBackClick={() => {
                    // Từ Exams luôn quay về StudentEdu
                    const homeRoute = user?.role === 'student' ? '/student' : '/teacher';
                    navigate(homeRoute, { replace: true });
                }}
            />
            <HeaderSpacer />

            <Box className="section" style={{ backgroundColor: '#fff', marginTop: '15px', padding: '15px' }}>
                <Box
                    className="section-header"
                    style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}
                >
                    <Text className="section-title" style={{ fontSize: '18px', fontWeight: 'bold' }}>
                        Bài tập theo môn học
                    </Text>
                </Box>

                {loading ? (
                    <Loading />
                ) : error ? (
                    <Text style={{ color: '#ff3b30', textAlign: 'center', padding: '20px' }}>{error}</Text>
                ) : !user?._id ? (
                    <Text style={{ color: '#666', textAlign: 'center', padding: '20px' }}>Đang xác thực...</Text>
                ) : (
                    <Box className="topic-list" style={{ display: 'flex', flexDirection: 'column', gap: '12px', marginTop: '15px' }}>
                        {/* TEST SECTION */}
                        <Box style={{ backgroundColor: '#fff3cd', padding: '10px', marginBottom: '10px', borderRadius: '8px' }}>
                            <Text style={{ fontWeight: 'bold', color: '#856404' }}>🔧 DEBUG INFO:</Text>
                            <Text style={{ fontSize: '12px', color: '#856404' }}>
                                useApiCache subjects: {subjects.length} | 
                                Test subjects: {testSubjects.length} | 
                                Loading: {loading ? 'Yes' : 'No'} | 
                                TestLoading: {testLoading ? 'Yes' : 'No'}
                            </Text>
                            
                            {testSubjects.length > 0 && (
                                <Box style={{ marginTop: '5px' }}>
                                    <Text style={{ fontSize: '12px', fontWeight: 'bold', color: '#856404' }}>Test subjects found:</Text>
                                    {testSubjects.slice(0, 3).map((subject, idx) => (
                                        <Text key={idx} style={{ fontSize: '10px', color: '#856404' }}>
                                            • {subject.name} ({subject._id})
                                        </Text>
                                    ))}
                                </Box>
                            )}
                        </Box>
                        
                        {subjects && subjects.length > 0 ? subjects.map((subject) => {
                            // Kiểm tra subject có hợp lệ không
                            if (!subject || !subject._id) return null;

                            // Đảm bảo statistics và subject._id tồn tại
                            const stats = (statistics && statistics[subject._id]) || {
                                totalExams: 0,
                                completedExams: 0,
                                completionRate: '0.00',
                            };
                            const progress = parseFloat(stats.completionRate) || 0;

                            return (
                                <Box
                                    key={subject._id}
                                    className="topic-item"
                                    style={{
                                        backgroundColor: '#fff',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        cursor: 'pointer',
                                    }}
                                    onClick={() => handleSubjectClick(subject._id)}
                                >
                                    <Box className="topic-info" style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                        <Box
                                            className="topic-icon"
                                            style={{
                                                width: '36px',
                                                height: '36px',
                                                borderRadius: '50%',
                                                backgroundColor: '#e8f0fe',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                fontSize: '18px',
                                                color: '#0068ff',
                                            }}
                                        >
                                            {getSubjectIcon(subject.name)}
                                        </Box>
                                        <Box className="topic-details">
                                            <Text className="topic-title" style={{ fontWeight: 'bold', fontSize: '15px' }}>
                                                {subject.name}
                                            </Text>
                                            <Text className="topic-meta" style={{ fontSize: '12px', color: '#666' }}>
                                                {stats.totalExams} Bài tập • {stats.completedExams} Hoàn thành
                                            </Text>
                                            <Box
                                                className="topic-progress"
                                                style={{
                                                    backgroundColor: '#eee',
                                                    height: '6px',
                                                    borderRadius: '3px',
                                                    width: '100%',
                                                    marginTop: '6px',
                                                    overflow: 'hidden',
                                                }}
                                            >
                                                <Box
                                                    className="progress-bar"
                                                    style={{ height: '100%', backgroundColor: '#0068ff', width: `${progress}%` }}
                                                />
                                            </Box>
                                        </Box>
                                    </Box>
                                    <Text style={{ fontSize: '20px' }}>▶️</Text>
                                </Box>
                            );
                        }) : (
                            <Box style={{ textAlign: 'center', padding: '20px' }}>
                                <Text style={{ color: '#666' }}>Chưa có môn học nào</Text>
                            </Box>
                        )}
                    </Box>
                )}
            </Box>

            <BottomNavigationEdu active="exercises" />
        </Box>
    );
};

export default Exercises;