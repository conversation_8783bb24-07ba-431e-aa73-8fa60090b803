import { useContext, useCallback, useEffect } from 'react';
import { Box, Text, Button, useNavigate, useParams, useLocation } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext'; // Giả sử bạn có context lưu thông tin user
import { authApi } from '../utils/api';
import { format } from 'date-fns';
import vi from 'date-fns/locale/vi';
import Loading from '../components/Loading';
import useApiCache from '../hooks/useApiCache';
import useSwipeNavigation from '../hooks/useSwipeNavigation';

const ExerciseDetail = () => {
    const { examId } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const { user } = useContext(AuthContext); // L<PERSON>y thông tin học sinh

    // Debug logging - Add this after the params/hooks
    useEffect(() => {
        console.log('=== EXERCISE DETAIL COMPONENT MOUNTED ===');
        console.log('examId from params:', examId);
        console.log('user from context:', user);
        console.log('location:', location);
        console.log('authApi available:', !!authApi);
        console.log('==========================================');
    }, [examId, user, location]);

    // Tạo các hàm gọi API riêng biệt
    const fetchExamDetails = useCallback(async () => {
        console.log('[fetchExamDetails] Called with:', { user: !!user, examId });
        if (!user || !examId) {
            console.log('[fetchExamDetails] Missing user or examId, returning null');
            return null;
        }

        try {
            console.log('[fetchExamDetails] Making API call to /exams/' + examId);
            // Lấy thông tin bài tập
            const examRes = await authApi.get(`/exams/${examId}`);
            console.log('[fetchExamDetails] API response:', examRes.data);
            return examRes.data.data;
        } catch (err) {
            console.error('[fetchExamDetails] Error:', err);
            throw new Error('Lỗi khi tải thông tin bài tập');
        }
    }, [user, examId]);

    const fetchExamResults = useCallback(async () => {
        console.log('[fetchExamResults] Called with:', { user: !!user, examId });
        if (!user || !examId) {
            console.log('[fetchExamResults] Missing user or examId, returning null');
            return null;
        }

        try {
            console.log('[fetchExamResults] Making API call to /exams/' + examId + '/results');
            // Lấy thống kê làm bài
            const resultsRes = await authApi.get(`/exams/${examId}/results`);
            console.log('[fetchExamResults] API response:', resultsRes.data);
            return resultsRes.data.data;
        } catch (err) {
            console.error('[fetchExamResults] Error:', err);
            throw new Error('Lỗi khi tải kết quả bài tập');
        }
    }, [user, examId]);

    const fetchActiveAttempt = useCallback(async () => {
        console.log('[fetchActiveAttempt] Called with:', { user: !!user, examId });
        if (!user || !examId) {
            console.log('[fetchActiveAttempt] Missing user or examId, returning null');
            return null;
        }

        try {
            const endpoint = `/exams/students/${user._id}/exam-attempts?examId=${examId}&completed=false`;
            console.log('[fetchActiveAttempt] Making API call to:', endpoint);
            // Lấy lần làm đang dở
            const activeAttemptRes = await authApi.get(endpoint);
            console.log('[fetchActiveAttempt] API response:', activeAttemptRes.data);
            return activeAttemptRes.data.data[0] || null;
        } catch (err) {
            console.error('[fetchActiveAttempt] Error:', err);
            throw new Error('Lỗi khi tải lần làm đang dở');
        }
    }, [user, examId]);

    const fetchAttemptHistory = useCallback(async () => {
        console.log('[fetchAttemptHistory] Called with:', { user: !!user, examId });
        if (!user || !examId) {
            console.log('[fetchAttemptHistory] Missing user or examId, returning empty array');
            return [];
        }

        try {
            const endpoint = `/exams/students/${user._id}/exam-attempts?examId=${examId}&completed=true`;
            console.log('[fetchAttemptHistory] Making API call to:', endpoint);
            // Lấy lịch sử làm bài
            const historyRes = await authApi.get(endpoint);
            console.log('[fetchAttemptHistory] API response:', historyRes.data);
            return historyRes.data.data || [];
        } catch (err) {
            console.error('[fetchAttemptHistory] Error:', err);
            throw new Error('Lỗi khi tải lịch sử làm bài');
        }
    }, [user, examId]);

    // Debug useApiCache calls
    console.log('[ExerciseDetail] About to call useApiCache hooks');
    console.log('[ExerciseDetail] Conditions:', {
        hasUser: !!user,
        hasExamId: !!examId,
        enabled: !!user && !!examId
    });

    // Sử dụng useApiCache cho từng loại dữ liệu
    const {
        data: exam,
        loading: examLoading,
        error: examError
    } = useApiCache(fetchExamDetails, [user, examId], {
        cacheKey: `exam_details_${examId}`,
        enabled: !!user && !!examId,
        cacheTime: 5 * 60 * 1000, // 5 phút
    });

    const {
        data: results,
        loading: resultsLoading
    } = useApiCache(fetchExamResults, [user, examId], {
        cacheKey: `exam_results_${examId}`,
        enabled: !!user && !!examId,
        cacheTime: 5 * 60 * 1000,
    });

    const {
        data: activeAttempt,
        loading: attemptLoading,
        refetch: refetchActiveAttempt
    } = useApiCache(fetchActiveAttempt, [user, examId], {
        cacheKey: `active_attempt_${examId}_${user?._id}`,
        enabled: !!user && !!examId,
        cacheTime: 1 * 60 * 1000, // 1 phút (cần refresh thường xuyên hơn)
    });

    const {
        data: historyData,
        loading: historyLoading,
        refetch: refetchHistory
    } = useApiCache(fetchAttemptHistory, [user, examId], {
        cacheKey: `attempt_history_${examId}_${user?._id}`,
        enabled: !!user && !!examId,
        cacheTime: 5 * 60 * 1000,
    });

    // Đảm bảo history luôn là một mảng
    const history = Array.isArray(historyData) ? historyData : [];

    // Debug log after useApiCache calls
    console.log('[ExerciseDetail] useApiCache results:', {
        exam,
        examLoading,
        examError,
        results,
        resultsLoading,
        activeAttempt,
        attemptLoading,
        history: history.length,
        historyLoading
    });

    // Tính toán trạng thái loading và error tổng hợp
    const loading = examLoading || resultsLoading || attemptLoading || historyLoading;
    const error = examError;

    // Sử dụng hook useSwipeNavigation để hỗ trợ vuốt để quay lại
    useSwipeNavigation({
        threshold: 80,
        minVelocity: 0.3,
        swipeDirection: "right",
        preventDefaultOnSwipe: true
    });

    // Định dạng ngày
    const formatDate = (dateString) => {
        try {
            return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: vi });
        } catch {
            return 'N/A';
        }
    };

    // Tính thời gian còn lại cho lần làm đang dở
    const calculateRemainingTime = (startTime, timeLimit) => {
        const start = new Date(startTime).getTime();
        const now = Date.now();
        const end = start + timeLimit * 60 * 1000; // timeLimit tính bằng phút
        const remainingMs = end - now;
        if (remainingMs <= 0) return 'Hết thời gian';
        const minutes = Math.floor(remainingMs / 60000);
        return `${minutes} phút`;
    };

    // Bắt đầu làm bài mới
    const handleStartNewAttempt = async () => {
        try {
            const response = await authApi.post(`/exams/${examId}/attempt`);
            const attemptId = response.data.data._id;

            // Lưu lại thông tin trang hiện tại để có thể quay lại đúng vị trí
            navigate(`/attempt/${attemptId}`, {
                replace: false,
                state: {
                    tab: `exercises/${examId}`,
                    previousPath: location.pathname,
                    previousState: location.state,
                    examId: examId, // Thêm examId vào state để có thể quay lại trang này
                    subjectId: location.state?.subjectId // Giữ subjectId để duy trì luồng điều hướng
                },
            });

            // Refresh dữ liệu lần làm đang dở sau khi tạo mới
            refetchActiveAttempt();
        } catch (err) {
            console.error('Error starting new attempt:', err);
            alert('Lỗi khi bắt đầu bài làm mới');
        }
    };

    // Tiếp tục làm bài
    const handleContinueAttempt = (attemptId) => {
        navigate(`/attempt/${attemptId}`, {
            replace: false,
            state: {
                tab: `exercises/${examId}`,
                previousPath: location.pathname,
                previousState: location.state,
                examId: examId, // Thêm examId vào state để có thể quay lại trang này
                subjectId: location.state?.subjectId // Giữ subjectId để duy trì luồng điều hướng
            },
        });
    };

    // Xem chi tiết kết quả
    const handleViewDetails = (attemptId) => {
        navigate(`/attempt/${attemptId}/results`, {
            replace: false,
            state: {
                tab: `exercises/${examId}`,
                previousPath: location.pathname,
                previousState: location.state,
                examId: examId, // Thêm examId vào state để có thể quay lại trang này
                subjectId: location.state?.subjectId // Giữ subjectId để duy trì luồng điều hướng
            },
        });
    };

    if (error) {
        return <Text style={{ color: '#ff3b30', textAlign: 'center', padding: '20px' }}>{error}</Text>;
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px' }}>
            <HeaderEdu
                title="Chi tiết bài tập"
                showBackButton
                onBackClick={() => {
                    console.log('ExerciseDetail: Back button clicked');
                    console.log('ExerciseDetail: Location state:', location.state);

                    // Lấy subjectId trực tiếp từ state
                    const subjectId = location.state?.subjectId;

                    // Kiểm tra xem có đến từ trang danh sách bài tập của môn học không
                    const fromSubjectExercises = location.state?.previousPath?.includes('/subjects/') &&
                                               location.state?.previousPath?.includes('/exercises');

                    console.log('ExerciseDetail: subjectId:', subjectId);
                    console.log('ExerciseDetail: fromSubjectExercises:', fromSubjectExercises);

                    // Nếu có subjectId và đến từ trang danh sách bài tập, quay về trang đó
                    if (subjectId && fromSubjectExercises) {
                        navigate(`/subjects/${subjectId}/exercises`, { replace: true });
                        console.log(`ExerciseDetail: Navigating back to subject exercises: ${subjectId}`);
                    } else {
                        // Nếu không, quay về trang Exams
                        navigate('/exams', { replace: true });
                        console.log('ExerciseDetail: Navigating back to exams');
                    }
                }}
            />
            <HeaderSpacer />

            {loading ? (
                <Loading />
            ) : (
                <Box className="page-container" style={{ padding: '15px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
                    {/* Thông tin bài tập */}
                    <Box className="card">
                        <Box className="card-header" style={{ padding: '15px', borderBottom: '1px solid #f0f0f0', backgroundColor: '#f8f9fa' }}>
                            <Text className="card-header-title" style={{ fontSize: '18px', fontWeight: 'bold', color: '#333' }}>
                                {exam?.title}
                            </Text>
                            <Box style={{ marginTop: '5px', display: 'flex', alignItems: 'center' }}>
                                <Text className="badge badge-primary" style={{ padding: '4px 8px', borderRadius: '4px', fontSize: '12px', backgroundColor: '#e8f0fe', color: '#0068ff' }}>
                                    {exam?.subject?.name || 'N/A'}
                                </Text>
                                <Text className="badge badge-secondary" style={{ padding: '4px 8px', borderRadius: '4px', fontSize: '12px', backgroundColor: '#f0f0f0', color: '#666', marginLeft: '5px' }}>
                                    {exam?.subject?.code || 'N/A'}
                                </Text>
                            </Box>
                        </Box>
                        <Box className="card-body" style={{ padding: '15px' }}>
                            <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                <Text className="info-label">Thời gian làm bài:</Text>
                                <Text className="info-value">{exam?.timeLimit} phút</Text>
                            </Box>
                            <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                <Text className="info-label">Số câu mỗi lần làm:</Text>
                                <Text className="info-value">{exam?.questionsPerAttempt} / {exam?.totalQuestions} câu</Text>
                            </Box>
                            <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                <Text className="info-label">Trạng thái:</Text>
                                <Text className={`badge ${exam?.isActive ? 'badge-success' : 'badge-warning'}`}>
                                    {exam?.isActive ? 'Đang mở' : 'Đã đóng'}
                                </Text>
                            </Box>
                            <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                <Text className="info-label">Thời hạn:</Text>
                                <Box className="date-badge" style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                                    <Text style={{ fontSize: '14px' }}>📅</Text>
                                    <Text>{formatDate(exam?.endDate)}</Text>
                                </Box>
                            </Box>
                            <Box style={{ marginTop: '15px' }}>
                                <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px', color: '#0068ff' }}>
                                    Mô tả
                                </Text>
                                <Text className="description-text" style={{ fontSize: '14px', color: '#444', marginBottom: '15px' }}>
                                    {exam?.description}
                                </Text>
                                <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px', color: '#0068ff' }}>
                                    Hướng dẫn
                                </Text>
                                <Box className="instructions-box" style={{ backgroundColor: '#f8f9fa', borderRadius: '6px', padding: '12px', borderLeft: '3px solid #0068ff' }}>
                                    <Text className="description-text">{exam?.instructions}</Text>
                                </Box>

                                <Box className="action-buttons" style={{ display: 'flex', gap: '10px', padding: '15px' }}>
                                    {!activeAttempt && (
                                        <Button className="btn btn-outline" style={{ flex: 1 }} onClick={handleStartNewAttempt}>
                                            Bắt đầu làm bài
                                        </Button>
                                    )}
                                </Box>
                            </Box>
                        </Box>
                    </Box>

                    {/* Thống kê làm bài */}
                    <Box className="card">
                        <Box className="card-header" style={{ padding: '15px', borderBottom: '1px solid #f0f0f0', backgroundColor: '#f8f9fa' }}>
                            <Text className="card-header-title" style={{ fontSize: '18px', fontWeight: 'bold', color: '#333' }}>
                                Thống kê làm bài
                            </Text>
                        </Box>
                        <Box className="card-body" style={{ padding: '15px' }}>
                            <Box className="stats-container" style={{ display: 'flex', justifyContent: 'space-between', marginTop: '10px' }}>
                                <Box className="stat-item" style={{ textAlign: 'center', backgroundColor: '#f0f6ff', borderRadius: '6px', padding: '12px', flex: 1, margin: '0 5px' }}>
                                    <Text className="stat-value" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>{results?.totalAttempts || 0}</Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>Số lần làm</Text>
                                </Box>
                                <Box className="stat-item" style={{ textAlign: 'center', backgroundColor: '#f0f6ff', borderRadius: '6px', padding: '12px', flex: 1, margin: '0 5px' }}>
                                    <Text className="stat-value" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>{results?.bestScore?.toFixed(1) || '0.0'}</Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>Điểm cao nhất</Text>
                                </Box>
                                <Box className="stat-item" style={{ textAlign: 'center', backgroundColor: '#f0f6ff', borderRadius: '6px', padding: '12px', flex: 1, margin: '0 5px' }}>
                                    <Text className="stat-value" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>{results?.averageScore?.toFixed(1) || '0.0'}</Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>Điểm trung bình</Text>
                                </Box>
                            </Box>

                            {results?.latestAttempt && (
                                <Box style={{ marginTop: '20px' }}>
                                    <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px', color: '#0068ff' }}>
                                        Lần làm gần nhất
                                    </Text>
                                    <Box className="attempt-card" style={{ padding: '15px', marginBottom: '10px', borderRadius: '8px', backgroundColor: '#f9f9f9' }}>
                                        <Box className="attempt-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Text className="attempt-date" style={{ fontSize: '13px', color: '#666' }}>{formatDate(results?.latestAttempt?.endTime)}</Text>
                                            <Text className="attempt-status" style={{ fontSize: '13px', padding: '3px 8px', borderRadius: '12px', backgroundColor: '#e6f7e6', color: '#28a745' }}>
                                                Hoàn thành
                                            </Text>
                                        </Box>
                                        <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                            <Text className="info-label">Điểm số:</Text>
                                            <Text className="attempt-score" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>{results?.latestAttempt?.score?.toFixed(1) || '0.0'}</Text>
                                        </Box>
                                        <Button className="btn btn-outline" style={{ marginTop: '10px' }} onClick={() => handleViewDetails(results?.latestAttempt?._id)}>
                                            Xem chi tiết
                                        </Button>
                                    </Box>
                                </Box>
                            )}

                            {/* Lần làm đang dở */}
                            {activeAttempt && (
                                <Box style={{ marginTop: '10px' }}>
                                    <Box
                                        className="attempt-card"
                                        style={{ padding: '15px', marginBottom: '10px', borderRadius: '8px', border: '1px solid #ffc107', backgroundColor: '#fffbf0' }}
                                    >
                                        <Box className="attempt-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Text className="attempt-date" style={{ fontSize: '13px', color: '#666' }}>{formatDate(activeAttempt.startTime)}</Text>
                                            <Text className="attempt-status active-attempt" style={{ fontSize: '13px', padding: '3px 8px', borderRadius: '12px', backgroundColor: '#fff3cd', color: '#856404' }}>
                                                Đang làm
                                            </Text>
                                        </Box>
                                        <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                            <Text className="info-label">Đã trả lời:</Text>
                                            <Text className="info-value">{activeAttempt.questionsAnswered} / {exam?.questionsPerAttempt} câu</Text>
                                        </Box>
                                        <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                            <Text className="info-label">Thời gian còn lại:</Text>
                                            <Text className="info-value">{calculateRemainingTime(activeAttempt.startTime, exam?.timeLimit)}</Text>
                                        </Box>
                                        <Button className="btn" style={{ marginTop: '10px' }} onClick={() => handleContinueAttempt(activeAttempt._id)}>
                                            Tiếp tục làm bài
                                        </Button>
                                    </Box>
                                </Box>
                            )}

                            {/* Lịch sử làm bài */}
                            <Text className="history-title" style={{ fontSize: '16px', fontWeight: 'bold', margin: '15px 0 10px' }}>
                                Lịch sử làm bài
                            </Text>
                            {Array.isArray(history) && history.length > 0 ? (
                                <Box className="history-list" style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                                    {history.map((attempt) => (
                                        <Box key={attempt._id} className="attempt-card" style={{ padding: '15px', marginBottom: '10px', borderRadius: '8px', backgroundColor: '#f9f9f9' }}>
                                            <Box className="attempt-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <Text className="attempt-date" style={{ fontSize: '13px', color: '#666' }}>{formatDate(attempt?.endTime)}</Text>
                                                <Text className="attempt-status" style={{ fontSize: '13px', padding: '3px 8px', borderRadius: '12px', backgroundColor: '#e6f7e6', color: '#28a745' }}>
                                                    Hoàn thành
                                                </Text>
                                            </Box>
                                            <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                                <Text className="info-label">Điểm số:</Text>
                                                <Text className="attempt-score" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>{attempt?.score?.toFixed(1) || '0.0'}</Text>
                                            </Box>
                                            <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                                                <Text className="info-label">Câu đúng:</Text>
                                                <Text className="info-value">{attempt?.totalCorrect || 0} / {exam?.questionsPerAttempt || 0}</Text>
                                            </Box>
                                            <Button className="btn btn-outline" style={{ marginTop: '10px' }} onClick={() => handleViewDetails(attempt?._id)}>
                                                Xem chi tiết
                                            </Button>
                                        </Box>
                                    ))}
                                </Box>
                            ) : (
                                <Text className="history-empty" style={{ textAlign: 'center', padding: '15px', color: '#666', fontStyle: 'italic' }}>
                                    Chưa có lịch sử làm bài
                                </Text>
                            )}
                        </Box>
                    </Box>
                </Box>
            )}
            <BottomNavigationEdu active="exercises" />
        </Box>
    );
};

export default ExerciseDetail;