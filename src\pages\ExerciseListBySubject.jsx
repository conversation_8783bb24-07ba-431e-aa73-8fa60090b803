import { useState, useContext, useCallback, useEffect } from 'react';
import { Box, Text, Button, useNavigate, useLocation } from 'zmp-ui';
import { useParams } from 'react-router-dom';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { format } from 'date-fns';
import vi from 'date-fns/locale/vi';
import Loading from '../components/Loading';
import useApiCache from '../hooks/useApiCache';
import useSwipeNavigation from '../hooks/useSwipeNavigation';

const ExerciseListBySubject = () => {
  const { subjectId } = useParams(); // Lấy subjectId từ URL
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useContext(AuthContext);
  const [subjectName, setSubjectName] = useState(''); // Lưu tên môn học

  // Debug logging
  useEffect(() => {
    console.log('=== EXERCISE LIST BY SUBJECT MOUNTED ===');
    console.log('subjectId:', subjectId);
    console.log('user:', user);
    console.log('authApi:', !!authApi);
    console.log('==========================================');
  }, [subjectId, user]);

  // Tạo hàm gọi API với error handling
  const fetchExams = useCallback(async () => {
    console.log('[ExerciseListBySubject] fetchExams called with:', { user: !!user, subjectId });
    
    if (!user || !subjectId) {
      console.log('[ExerciseListBySubject] Missing user or subjectId');
      return [];
    }

    try {
      console.log('[ExerciseListBySubject] Making API call to /exams?subjectId=' + subjectId);
      const response = await authApi.get(`/exams?subjectId=${subjectId}`);
      console.log('[ExerciseListBySubject] API response:', response.data);
      
      const data = response.data;
      if (data.success) {
        // Lấy tên môn học từ bài tập đầu tiên (nếu có)
        if (data.data && data.data.length > 0) {
          setSubjectName(data.data[0]?.subject?.name || '');
        }
        return data.data || [];
      } else {
        throw new Error('Không tìm thấy bài tập');
      }
    } catch (err) {
      console.error('[ExerciseListBySubject] Error:', err);
      throw new Error('Lỗi khi tải danh sách bài tập');
    }
  }, [user, subjectId]);

  // Sử dụng hook useApiCache để cache kết quả API
  const {
    data: exams = [],
    loading,
    error
  } = useApiCache(
    fetchExams,
    [user, subjectId],
    {
      cacheKey: `exams_by_subject_${subjectId}`,
      enabled: !!user && !!subjectId,
      cacheTime: 5 * 60 * 1000, // Cache 5 phút
      // Removed staleTime as it's not supported in current implementation
    }
  );

  console.log('[ExerciseListBySubject] State:', {
    exams: exams.length,
    loading,
    error,
    subjectName
  });

  // Định dạng ngày
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi });
    } catch {
      return 'N/A';
    }
  };

  // Điều hướng đến chi tiết bài tập
  const handleViewDetails = (examId) => {
    // Lưu lại trang hiện tại để có thể quay lại đúng vị trí
    // Kiểm tra xem location.state có chứa previousPath không
    const previousPath = location.state?.previousPath || '/exams';

    navigate(`/exercises/${examId}`, {
      replace: false,
      state: {
        tab: 'exams',
        previousPath: location.pathname, // Lưu đường dẫn hiện tại (trang danh sách bài tập của môn học)
        subjectId: subjectId, // Lưu subjectId để có thể quay lại trang này khi vuốt
        // Nếu đang từ trang Exams, không lưu lại state hiện tại
        previousState: previousPath === '/exams' ? null : location.state
      },
    });
  };

  // Sử dụng hook useSwipeNavigation để hỗ trợ vuốt để quay lại
  useSwipeNavigation({
    threshold: 80,
    minVelocity: 0.3,
    swipeDirection: "right",
    preventDefaultOnSwipe: true
  });

  if (error) {
    return <Text style={{ color: '#ff3b30', textAlign: 'center', padding: '20px' }}>{error}</Text>;
  }

  return (
    <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px' }}>
      <HeaderEdu
        title={`Bài tập ${subjectName}`}
        showBackButton
        onBackClick={() => {
          // Luôn quay về trang Exams khi nhấn back từ trang này
          navigate('/exams', { replace: true });
        }}
      />
      <HeaderSpacer />

      {loading ? (
        <Loading />
      ) : (
        <Box className="page-container" style={{ padding: '15px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
          {exams.length === 0 ? (
            <Text style={{ textAlign: 'center', padding: '20px', color: '#666', fontStyle: 'italic' }}>
              Chưa có bài tập nào cho môn học này
            </Text>
          ) : (
            Array.isArray(exams) && exams.length > 0 ? exams.map((exam) => {
              // Safety check for exam data
              if (!exam || !exam._id) {
                console.warn('[ExerciseListBySubject] Invalid exam data:', exam);
                return null;
              }

              return (
              <Box key={exam._id} className="card" style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', overflow: 'hidden' }}>
                <Box className="card-header" style={{ padding: '15px', borderBottom: '1px solid #f0f0f0', backgroundColor: '#f8f9fa', position: 'relative' }}>
                  <Text className="card-header-title" style={{ fontSize: '18px', fontWeight: 'bold', color: '#333' }}>
                    {exam.title || 'Không có tiêu đề'}
                  </Text>
                  <Box style={{ marginTop: '5px', display: 'flex', alignItems: 'center', gap: '5px' }}>
                    <Text className="badge badge-primary" style={{ padding: '4px 8px', borderRadius: '4px', fontSize: '12px', backgroundColor: '#e8f0fe', color: '#0068ff' }}>
                      {exam.subject?.name || 'Không có tên môn'}
                    </Text>
                    <Text className="badge badge-secondary" style={{ padding: '4px 8px', borderRadius: '4px', fontSize: '12px', backgroundColor: '#f0f0f0', color: '#666' }}>
                      {exam.subject?.code || 'N/A'}
                    </Text>
                  </Box>
                  <Text
                    className={`status-badge ${exam.isActive ? 'status-active' : 'status-inactive'}`}
                    style={{ position: 'absolute', top: '15px', right: '15px', padding: '4px 8px', borderRadius: '4px', fontSize: '12px', fontWeight: '500' }}
                  >
                    {exam.isActive ? 'Đang mở' : 'Đã đóng'}
                  </Text>
                </Box>
                <Box className="card-body" style={{ padding: '15px' }}>
                  <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                    <Text className="info-label" style={{ fontWeight: '500', color: '#666', fontSize: '14px' }}>
                      Thời gian làm bài:
                    </Text>
                    <Text className="info-value" style={{ fontWeight: '500', color: '#333', fontSize: '14px' }}>
                      {exam.timeLimit || 0} phút
                    </Text>
                  </Box>
                  <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                    <Text className="info-label" style={{ fontWeight: '500', color: '#666', fontSize: '14px' }}>
                      Số câu mỗi lần làm:
                    </Text>
                    <Text className="info-value" style={{ fontWeight: '500', color: '#333', fontSize: '14px' }}>
                      {exam.questionsPerAttempt || 0} / {exam.totalQuestions || 0} câu
                    </Text>
                  </Box>
                  <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                    <Text className="info-label" style={{ fontWeight: '500', color: '#666', fontSize: '14px' }}>
                      Thời hạn:
                    </Text>
                    <Box className="date-badge" style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                      <Text style={{ fontSize: '14px' }}>📅</Text>
                      <Text style={{ fontWeight: '500', color: '#333', fontSize: '14px' }}>{formatDate(exam.endDate)}</Text>
                    </Box>
                  </Box>
                  <Box className="info-row" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', alignItems: 'center' }}>
                    <Text className="info-label" style={{ fontWeight: '500', color: '#666', fontSize: '14px' }}>
                      Lớp áp dụng:
                    </Text>
                    <Text className="info-value" style={{ fontWeight: '500', color: '#333', fontSize: '14px' }}>
                      {Array.isArray(exam.classes) ? exam.classes.map((cls) => cls?.name || 'N/A').join(', ') : 'N/A'}
                    </Text>
                  </Box>
                  <Box style={{ marginTop: '15px' }}>
                    <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px', color: '#0068ff' }}>
                      Mô tả
                    </Text>
                    <Text className="description-text" style={{ fontSize: '14px', color: '#444', marginBottom: '15px', lineHeight: '1.6' }}>
                      {exam.description || 'Không có mô tả'}
                    </Text>
                  </Box>
                  {exam.attempts && (
                    <Box style={{ marginTop: '15px' }}>
                      <Text className="section-title" style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px', color: '#0068ff' }}>
                        Thống kê cá nhân
                      </Text>
                      <Box className="stats-container" style={{ display: 'flex', justifyContent: 'space-between', marginTop: '10px' }}>
                        <Box className="stat-item" style={{ textAlign: 'center', backgroundColor: '#f0f6ff', borderRadius: '6px', padding: '12px', flex: 1, margin: '0 5px' }}>
                          <Text className="stat-value" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>
                            {exam.attempts?.totalAttempts || 0}
                          </Text>
                          <Text className="stat-label" style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                            Số lần làm
                          </Text>
                        </Box>
                        <Box className="stat-item" style={{ textAlign: 'center', backgroundColor: '#f0f6ff', borderRadius: '6px', padding: '12px', flex: 1, margin: '0 5px' }}>
                          <Text className="stat-value" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>
                            {exam.attempts?.bestScore?.toFixed(1) || '0.0'}
                          </Text>
                          <Text className="stat-label" style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                            Điểm cao nhất
                          </Text>
                        </Box>
                      </Box>
                    </Box>
                  )}
                  <Button
                    className="btn"
                    style={{ padding: '12px 20px', backgroundColor: '#0068ff', color: 'white', borderRadius: '6px', fontWeight: '500', fontSize: '14px', width: '100%', marginTop: '15px' }}
                    onClick={() => handleViewDetails(exam._id)}
                  >
                    Xem chi tiết
                  </Button>
                </Box>
              </Box>
              );
            }).filter(Boolean) : (
              <Text style={{ textAlign: 'center', padding: '20px', color: '#666', fontStyle: 'italic' }}>
                Chưa có bài tập nào cho môn học này
              </Text>
            )
          )}
        </Box>
      )}

      <BottomNavigationEdu active="exercises" />
    </Box>
  );
};

export default ExerciseListBySubject;